# OLED功能移植指南

## 概述

本指南将帮助您将当前工程中的OLED显示功能移植到新的STM32项目中。该OLED模块基于SSD1306控制器，使用I2C接口通信，分辨率为128x32像素。

## 硬件要求

- **OLED屏幕**: 0.91寸 SSD1306控制器，128x32分辨率
- **通信接口**: I2C
- **电源**: 3.3V或5V（根据OLED模块规格）
- **连接引脚**: 
  - SCL: PB10 (I2C2_SCL)
  - SDA: PB11 (I2C2_SDA)

## 需要移植的文件

### 1. 核心驱动文件
```
User/Module/oled/
├── oled.h          # OLED驱动头文件
├── oled.c          # OLED驱动实现文件
├── oledfont.h      # 字体数据文件
└── oledpic.h       # 图片数据文件（可选）
```

### 2. 应用层文件
```
User/App/
├── oled_app.h      # OLED应用层头文件
└── oled_app.c      # OLED应用层实现文件
```

## 移植步骤

### 步骤1: 复制文件

将以下文件复制到新项目中：

1. **复制驱动文件**
   ```
   源路径: User/Module/oled/
   目标路径: [新项目]/Drivers/OLED/ 或 [新项目]/Components/OLED/
   
   文件列表:
   - oled.h
   - oled.c  
   - oledfont.h
   - oledpic.h (可选)
   ```

2. **复制应用文件**
   ```
   源路径: User/App/
   目标路径: [新项目]/Application/ 或 [新项目]/App/
   
   文件列表:
   - oled_app.h
   - oled_app.c
   ```

### 步骤2: 配置I2C接口

#### 2.1 在STM32CubeMX中配置I2C2

1. 打开STM32CubeMX
2. 在Pinout & Configuration中找到I2C2
3. 配置引脚:
   - PB10 → I2C2_SCL
   - PB11 → I2C2_SDA
4. 配置I2C参数:
   - I2C Speed Mode: Standard Mode
   - I2C Clock Speed: 400000 Hz
   - Duty Cycle: 2

#### 2.2 手动配置I2C（如果不使用CubeMX）

在`i2c.h`中添加：
```c
extern I2C_HandleTypeDef hi2c2;
void MX_I2C2_Init(void);
```

在`i2c.c`中添加I2C2初始化代码（参考原工程的i2c.c文件）。

### 步骤3: 修改包含路径

在新项目的编译器设置中添加包含路径：
```
[新项目]/Drivers/OLED
[新项目]/Application
```

或在Keil MDK中的C/C++选项卡的Include Paths中添加相应路径。

### 步骤4: 修改依赖关系

#### 4.1 修改oled.c中的包含文件

确保`oled.c`文件顶部的包含语句正确：
```c
#include "oled.h"
#include "oledfont.h"
#include "i2c.h"        // 确保能找到I2C配置
```

#### 4.2 修改oled_app.c中的包含文件

根据新项目结构修改包含路径：
```c
#include "oled_app.h"
#include "oled.h"
#include <stdio.h>
#include <stdarg.h>
```

### 步骤5: 适配硬件接口

#### 5.1 确认I2C接口

在`oled.c`中，确认使用的I2C接口：
```c
// 第41行和第45行，确认使用hi2c2
void OLED_Write_cmd(uint8_t cmd)
{
    HAL_I2C_Mem_Write(&hi2c2, 0x78, 0x00, I2C_MEMADD_SIZE_8BIT, &cmd, 1, 10);
}

void OLED_Write_data(uint8_t data)
{
    HAL_I2C_Mem_Write(&hi2c2, 0x78, 0x40, I2C_MEMADD_SIZE_8BIT, &data, 1, 10);
}
```

如果新项目使用不同的I2C接口（如hi2c1），需要修改这两个函数中的`&hi2c2`为相应的接口。

#### 5.2 修改OLED地址（如需要）

如果OLED模块的I2C地址不同，修改`oled.h`中的地址定义：
```c
#define OLED_ADDR 0x78  // 根据实际OLED模块调整
```

### 步骤6: 初始化和使用

#### 6.1 在主程序中初始化

在`main.c`中添加初始化代码：
```c
#include "oled_app.h"

int main(void)
{
    // ... 其他初始化代码
    
    MX_I2C2_Init();     // 初始化I2C2
    my_oled_init();     // 初始化OLED
    
    // ... 主循环
    while (1)
    {
        // 可以调用oled_task()或直接使用OLED函数
    }
}
```

#### 6.2 基本使用示例

```c
// 清屏
OLED_Clear();

// 显示字符串
OLED_ShowStr(0, 0, "Hello World", 8);

// 显示数字
OLED_ShowNum(0, 1, 12345, 5, 8);

// 显示浮点数
OLED_ShowFloat(0, 2, 3.14159, 2, 8);

// 使用printf风格显示
oled_printf(0, 3, "Value: %d", 100);
```

## 注意事项

### 1. 依赖清理

原工程中的`oled_app.c`包含了一些特定的外部变量和函数：
- `get_yaw()` - 获取偏航角函数
- `Digtal` - 数字量变量  
- `system_mode` - 系统模式变量

移植时需要：
- 删除或注释掉`oled_task()`函数中对这些变量的引用
- 或者提供相应的替代实现

### 2. 字体和图片资源

- `oledfont.h`包含6x8和8x16两种ASCII字体
- `oledfont.h`还包含中文字体数据（Hzk和Hzb数组）
- `oledpic.h`包含示例图片数据
- 根据需要可以删除不需要的字体或图片数据以节省存储空间

### 3. 内存使用

- `oled_printf`函数使用512字节的缓冲区
- 如果内存紧张，可以减小缓冲区大小

### 4. 时序要求

- OLED初始化需要100ms延时
- I2C通信超时设置为10ms
- 根据实际应用调整这些参数

## 测试验证

移植完成后，建议按以下步骤测试：

1. **基本通信测试**
   ```c
   OLED_Init();
   OLED_Clear();
   ```

2. **显示测试**
   ```c
   OLED_ShowStr(0, 0, "Test", 8);
   ```

3. **功能测试**
   ```c
   oled_printf(0, 1, "Count: %d", 123);
   ```

## 常见问题

1. **显示乱码**: 检查I2C地址和引脚配置
2. **无显示**: 检查电源连接和I2C时序
3. **编译错误**: 检查包含路径和文件依赖关系

## 总结

按照以上步骤，您应该能够成功将OLED功能移植到新的STM32项目中。移植的核心是正确配置I2C接口和处理文件依赖关系。
